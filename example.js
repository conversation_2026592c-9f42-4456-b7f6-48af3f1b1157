/**
 * 深海传说 Story资源获取示例
 */

const StoryResourceSimulator = require('./story_resource_simulator');
const fs = require('fs');

async function runExamples() {
    console.log('=== 深海传说 Story资源获取示例 ===\n');

    // 示例1: 基本使用
    console.log('示例1: 基本使用');
    try {
        const simulator = new StoryResourceSimulator({
            baseUrl: 'https://tonofura-w-cdn-client.deepone-online.com',
            apiPath: '/api/story/getResource'
        });

        console.log('正在获取Story ID 1001的资源...');
        const story1 = await simulator.getStoryResource(['1001'], 0);
        console.log('获取成功:', JSON.stringify(story1, null, 2));
        
    } catch (error) {
        console.log('获取失败 (这是预期的，因为需要有效的认证):', error.message);
    }
    console.log('');

    // 示例2: 批量获取
    console.log('示例2: 批量获取多个Story');
    try {
        const simulator = new StoryResourceSimulator();
        
        console.log('正在批量获取Story资源...');
        const stories = await simulator.getMultipleStoryResources([
            ['1001'],
            ['1002', '1003'],
            ['1004']
        ], 0);
        console.log('批量获取成功:', stories.length, '组数据');
        
    } catch (error) {
        console.log('批量获取失败:', error.message);
    }
    console.log('');

    // 示例3: 带认证的请求
    console.log('示例3: 带认证的请求');
    try {
        const simulator = new StoryResourceSimulator({
            baseUrl: 'https://tonofura-w-cdn-client.deepone-online.com',
            headers: {
                'X-Game-Version': '1.273.0',
                'X-Device-ID': 'your_device_id'
            }
        });

        // 设置认证信息 (需要从真实客户端获取)
        simulator.setAuth('your_session_token', 'your_user_id');
        
        console.log('正在使用认证信息获取Story资源...');
        const authenticatedStory = await simulator.getStoryResource(['1001'], 0);
        console.log('认证获取成功:', authenticatedStory);
        
    } catch (error) {
        console.log('认证获取失败:', error.message);
    }
    console.log('');

    // 示例4: 错误处理
    console.log('示例4: 错误处理演示');
    try {
        const simulator = new StoryResourceSimulator({
            baseUrl: 'https://invalid-server.com'
        });
        
        await simulator.getStoryResource(['1001'], 0);
        
    } catch (error) {
        console.log('捕获到错误 (演示错误处理):', error.message);
    }
    console.log('');

    // 示例5: 保存到文件
    console.log('示例5: 保存Story数据到文件');
    try {
        // 模拟一个成功的响应数据
        const mockStoryData = {
            success: true,
            data: {
                storyId: '1001',
                title: '第一章：深海的呼唤',
                content: [
                    {
                        type: 'text',
                        content: '在深邃的海底，传说中的古老文明正在苏醒...'
                    },
                    {
                        type: 'character',
                        name: '主角',
                        dialogue: '这里...是什么地方？'
                    }
                ],
                resources: {
                    images: ['bg_ocean_1.jpg', 'char_protagonist.png'],
                    sounds: ['bgm_mysterious.mp3', 'se_wave.wav']
                }
            }
        };

        const filename = `story_1001_${Date.now()}.json`;
        fs.writeFileSync(filename, JSON.stringify(mockStoryData, null, 2));
        console.log(`模拟Story数据已保存到: ${filename}`);
        
    } catch (error) {
        console.log('保存文件失败:', error.message);
    }
    console.log('');

    console.log('=== 示例运行完成 ===');
    console.log('\n使用说明:');
    console.log('1. 修改config.json中的服务器配置');
    console.log('2. 获取有效的认证信息 (sessionToken, userId等)');
    console.log('3. 运行: node cli.js <story_ids>');
    console.log('4. 或者在代码中使用StoryResourceSimulator类');
}

// 如果直接运行此文件，执行示例
if (require.main === module) {
    runExamples().catch(console.error);
}

module.exports = { runExamples };
