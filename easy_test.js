/**
 * 简易测试工具 - 多种方便的输入方式
 */

const StoryResourceSimulator = require('./story_resource_simulator');
const fs = require('fs');
const path = require('path');

// 方法1: 从文件读取认证信息
function loadAuthFromFile(filename = 'auth.txt') {
    try {
        const content = fs.readFileSync(filename, 'utf8').trim();
        return parseAuthFromText(content);
    } catch (error) {
        return null;
    }
}

// 方法2: 从环境变量读取
function loadAuthFromEnv() {
    const authHeader = process.env.DEEPONE_AUTH;
    if (authHeader) {
        return parseAuthFromText(authHeader);
    }
    return null;
}

// 方法3: 解析各种格式的文本
function parseAuthFromText(text) {
    const oauthParams = {};
    
    // 清理文本，移除多余的空白和换行
    let cleanText = text.replace(/\s+/g, ' ').trim();
    
    // 处理不同的输入格式
    
    // 格式1: 完整的请求头（从开发者工具复制的原始格式）
    if (cleanText.includes('authorization:') || cleanText.includes('Authorization:')) {
        const authMatch = cleanText.match(/authorization:\s*(.+)/i);
        if (authMatch) {
            cleanText = authMatch[1].trim();
        }
    }
    
    // 格式2: cURL格式 -H "Authorization: ..."
    if (cleanText.includes('-H') && cleanText.includes('Authorization')) {
        const curlMatch = cleanText.match(/-H\s+["']Authorization:\s*([^"']+)["']/i);
        if (curlMatch) {
            cleanText = curlMatch[1].trim();
        }
    }
    
    // 格式3: 只有OAuth部分
    if (cleanText.startsWith('OAuth')) {
        // 直接使用
    } else {
        // 可能缺少OAuth前缀，尝试查找
        const oauthMatch = cleanText.match(/OAuth\s+.+/i);
        if (oauthMatch) {
            cleanText = oauthMatch[0];
        }
    }
    
    // 移除OAuth realm部分
    cleanText = cleanText.replace(/^OAuth\s+realm="[^"]*"\s*/i, '');
    
    // 提取参数，支持多种引号格式
    const patterns = [
        /(\w+)="([^"]*)"/g,           // 标准格式: key="value"
        /(\w+)='([^']*)'/g,           // 单引号: key='value'
        /(\w+)=([^\s]+)/g             // 无引号: key=value
    ];
    
    for (const pattern of patterns) {
        let match;
        while ((match = pattern.exec(cleanText)) !== null) {
            oauthParams[match[1]] = match[2];
        }
    }
    
    return Object.keys(oauthParams).length > 0 ? oauthParams : null;
}

// 方法4: 交互式输入
async function interactiveInput() {
    const readline = require('readline');
    const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
    });
    
    return new Promise((resolve) => {
        console.log('请粘贴Authorization头信息（支持多行，输入空行结束）:');
        console.log('提示: 直接从浏览器开发者工具复制粘贴即可，无需处理引号');
        console.log('---');
        
        let lines = [];
        
        rl.on('line', (line) => {
            if (line.trim() === '') {
                rl.close();
                const fullText = lines.join(' ');
                const auth = parseAuthFromText(fullText);
                resolve(auth);
            } else {
                lines.push(line);
            }
        });
    });
}

// 主测试函数
async function easyTest(storyId = '100503') {
    console.log('=== 简易测试工具 ===\n');
    
    let oauthParams = null;
    
    // 尝试多种方式获取认证信息
    console.log('正在尝试获取认证信息...');
    
    // 方法1: 从命令行参数
    if (process.argv[2]) {
        console.log('✓ 从命令行参数读取');
        oauthParams = parseAuthFromText(process.argv[2]);
    }
    
    // 方法2: 从文件读取
    if (!oauthParams) {
        oauthParams = loadAuthFromFile('auth.txt');
        if (oauthParams) {
            console.log('✓ 从 auth.txt 文件读取');
        }
    }
    
    // 方法3: 从环境变量读取
    if (!oauthParams) {
        oauthParams = loadAuthFromEnv();
        if (oauthParams) {
            console.log('✓ 从环境变量读取');
        }
    }
    
    // 方法4: 交互式输入
    if (!oauthParams) {
        console.log('未找到认证信息，启动交互式输入...\n');
        oauthParams = await interactiveInput();
    }
    
    if (!oauthParams || Object.keys(oauthParams).length === 0) {
        console.log('❌ 无法获取有效的认证信息');
        showUsageHelp();
        return;
    }
    
    console.log('\n解析到的OAuth参数:');
    Object.entries(oauthParams).forEach(([key, value]) => {
        if (key === 'oauth_signature') {
            console.log(`  ${key}: ${value.substring(0, 10)}...`);
        } else {
            console.log(`  ${key}: ${value}`);
        }
    });
    
    // 执行测试
    try {
        const simulator = new StoryResourceSimulator({
            baseUrl: 'https://tonofura-web-w.deepone-online.com',
            apiPath: '/deep-one/api/story/getResource',
            oauthConfig: oauthParams
        });
        
        console.log(`\n正在获取Story ID: ${storyId}...`);
        const startTime = Date.now();
        const storyData = await simulator.getStoryResource([storyId], 0);
        const endTime = Date.now();
        
        console.log(`✅ 成功！耗时: ${endTime - startTime}ms`);
        
        // 保存数据
        const filename = `story_${storyId}_${Date.now()}.json`;
        fs.writeFileSync(filename, JSON.stringify(storyData, null, 2));
        console.log(`📁 数据已保存到: ${filename}`);
        
        // 显示数据摘要
        console.log('\n📊 数据摘要:');
        if (storyData && typeof storyData === 'object') {
            console.log(`- 数据类型: Object`);
            console.log(`- 主要字段: ${Object.keys(storyData).join(', ')}`);
            console.log(`- JSON大小: ${JSON.stringify(storyData).length} 字符`);
        }
        
    } catch (error) {
        console.log('❌ 请求失败:', error.message);
        
        if (error.message.includes('401')) {
            console.log('\n💡 认证失败，请尝试:');
            console.log('1. 重新从浏览器获取最新的Authorization头');
            console.log('2. 确保OAuth签名未过期');
        }
    }
}

function showUsageHelp() {
    console.log('\n=== 使用方法 ===');
    console.log('\n方法1: 命令行参数');
    console.log('node easy_test.js "你的authorization头" [story_id]');
    
    console.log('\n方法2: 创建 auth.txt 文件');
    console.log('将authorization头内容保存到 auth.txt 文件中，然后运行:');
    console.log('node easy_test.js [story_id]');
    
    console.log('\n方法3: 环境变量');
    console.log('设置环境变量后运行:');
    console.log('set DEEPONE_AUTH="你的authorization头"');
    console.log('node easy_test.js [story_id]');
    
    console.log('\n方法4: 交互式输入');
    console.log('直接运行，然后按提示粘贴:');
    console.log('node easy_test.js [story_id]');
    
    console.log('\n支持的输入格式:');
    console.log('- 浏览器开发者工具的原始格式');
    console.log('- cURL命令格式');
    console.log('- 纯OAuth字符串');
    console.log('- 多行文本（自动合并）');
}

// 创建示例auth.txt文件
function createExampleAuthFile() {
    const exampleContent = `# 将你的authorization头粘贴到下面，替换这些示例内容
# 支持多行，程序会自动处理

authorization: OAuth realm="Users" oauth_token="你的token" xoauth_requestor_id="你的requestor_id" oauth_consumer_key="*********************************" oauth_signature_method="HMAC-SHA256" oauth_nonce="你的nonce" oauth_timestamp="你的timestamp" oauth_signature="你的signature"

# 或者直接粘贴OAuth部分:
# OAuth realm="Users" oauth_token="..." ...
`;
    
    if (!fs.existsSync('auth.txt')) {
        fs.writeFileSync('auth.txt', exampleContent);
        console.log('📝 已创建示例 auth.txt 文件，请编辑后使用');
    }
}

// 主函数
async function main() {
    const storyId = process.argv[3] || process.argv[2] || '100503';
    
    // 如果没有任何参数，显示帮助并创建示例文件
    if (process.argv.length === 2) {
        showUsageHelp();
        createExampleAuthFile();
        return;
    }
    
    await easyTest(storyId);
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = { easyTest, parseAuthFromText, loadAuthFromFile };
