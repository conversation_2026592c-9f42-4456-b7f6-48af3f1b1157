# 深海传说 Story资源获取 - 快速开始

## 🚀 立即开始（推荐）

### 步骤1: 获取认证信息

1. **打开游戏**: 在浏览器中访问深海传说游戏
2. **打开开发者工具**: 按 `F12` 键
3. **切换到Network标签**: 点击 "Network" 或"网络"
4. **触发Story请求**: 在游戏中查看任意Story内容
5. **找到API请求**: 寻找对 `/deep-one/api/story/getResource` 的请求
6. **复制认证头**: 
   - 点击该请求
   - 查看 "Request Headers" 
   - 复制 `authorization` 字段的完整值

### 步骤2: 运行测试

```bash
# 使用你复制的authorization头（记得加引号）
node quick_test.js "OAuth realm=\"Users\" oauth_token=\"你的token\" ..." "100503"
```

## 📋 详细步骤

### 获取Authorization头的完整示例

你需要复制类似这样的内容：
```
OAuth realm="Users" oauth_token="2qqR9jHrECutDsVMjLbBHa" xoauth_requestor_id="7nkQlgizFfm3Z1om7Qsnmf" oauth_consumer_key="*********************************" oauth_signature_method="HMAC-SHA256" oauth_nonce="8351254459087218" oauth_timestamp="1755924912" oauth_signature="8QbB0bmRRFbaZz+F38FL65ZoEuIx3eIYnqxwvl346mQ="
```

### 运行示例

```bash
# 测试默认Story ID (100503)
node quick_test.js "你的完整authorization头"

# 测试指定的Story ID
node quick_test.js "你的完整authorization头" "100501"

# 如果没有参数，会显示帮助信息
node quick_test.js
```

## ⚠️ 重要注意事项

### 1. 时效性
- OAuth签名通常在几分钟内就会过期
- 获取authorization头后请立即使用
- 如果收到401错误，重新获取最新的头信息

### 2. 格式要求
- 必须包含完整的authorization头
- 记得在命令行中使用引号包围
- 不要遗漏任何参数

### 3. Story ID
- 你提到的 `100503` 是一个有效的Story ID
- 可以尝试其他ID，如 `100501`, `100502` 等
- 如果ID不存在会返回相应错误

## 🔧 故障排除

### 401 Unauthorized
```
原因: 认证信息过期或无效
解决: 重新从浏览器获取最新的authorization头
```

### 403 Forbidden  
```
原因: 权限不足或Story ID无权访问
解决: 检查Story ID是否正确，确认账号权限
```

### 404 Not Found
```
原因: Story ID不存在
解决: 尝试其他有效的Story ID
```

### 网络错误
```
原因: 网络连接问题
解决: 检查网络连接，确认服务器地址
```

## 📁 输出文件

成功获取后会生成：
- `story_[ID]_[timestamp].json` - 包含完整的Story数据

## 🔍 数据结构

获取到的Story数据通常包含：
- Story文本内容
- 角色对话
- 背景图片信息
- 音效文件信息
- 分支选择信息

## 💡 高级用法

### 批量获取多个Story
```bash
# 创建批量脚本
for id in 100501 100502 100503; do
    node quick_test.js "你的authorization头" "$id"
    sleep 2  # 添加延迟避免请求过快
done
```

### 自动化脚本
可以修改 `quick_test.js` 来：
- 自动解析多个Story ID
- 添加重试机制
- 自动刷新认证信息

## 📞 需要帮助？

如果遇到问题：
1. 确认按照步骤正确获取了authorization头
2. 检查命令行语法是否正确
3. 确认网络连接正常
4. 尝试不同的Story ID

## 🎯 成功示例

当一切正常时，你会看到类似这样的输出：
```
=== 快速测试工具 ===

正在解析Authorization头...
解析到的OAuth参数:
  oauth_token: 2qqR9jHrECutDsVMjLbBHa
  xoauth_requestor_id: 7nkQlgizFfm3Z1om7Qsnmf
  ...

正在获取Story ID: 100503
---

✅ 成功获取Story资源！耗时: 1234ms

Story数据结构:
数据类型: Object
主要字段: ['success', 'data', 'message']

📁 完整数据已保存到: story_100503_1234567890.json
```
