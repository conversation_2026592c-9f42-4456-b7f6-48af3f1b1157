# 深海传说 Story资源获取使用指南

## 快速开始

### 1. 运行示例

```bash
# 查看示例代码
node example.js

# 运行测试
node test.js
```

### 2. 命令行使用

```bash
# 获取单个Story
node cli.js 1001

# 获取多个Story
node cli.js 1001,1002,1003

# 保存到文件
node cli.js 1001 --output story_1001.json

# 获取成人内容
node cli.js 1001 --adult 1
```

## 配置说明

### 修改 config.json

```json
{
  "server": {
    "baseUrl": "https://your-game-server.com",
    "apiPath": "/api/story/getResource",
    "timeout": 30000
  },
  "auth": {
    "sessionToken": "your_session_token_here",
    "userId": "your_user_id_here",
    "deviceId": "your_device_id_here",
    "gameVersion": "1.273.0"
  }
}
```

## 获取认证信息

### 方法1: 浏览器开发者工具

1. 在浏览器中打开游戏
2. 按F12打开开发者工具
3. 切换到Network标签
4. 在游戏中触发Story相关操作
5. 查看请求头中的认证信息

### 方法2: 抓包工具

使用Fiddler、Charles或Wireshark等工具：

1. 配置代理设置
2. 启动游戏客户端
3. 捕获HTTP/HTTPS请求
4. 分析请求头和参数

### 方法3: 从客户端文件提取

从游戏客户端的本地存储中查找：
- LocalStorage
- SessionStorage  
- Cookies
- 配置文件

## API端点发现

### 常见的API路径模式

```
/api/story/getResource
/story/getResource
/api/v1/story/getResource
/game/story/resource
/resource/story
```

### 测试API端点

```javascript
const endpoints = [
    '/api/story/getResource',
    '/story/getResource',
    '/api/v1/story/getResource'
];

for (const endpoint of endpoints) {
    try {
        const simulator = new StoryResourceSimulator({
            apiPath: endpoint
        });
        const result = await simulator.getStoryResource(['1001'], 0);
        console.log(`成功的端点: ${endpoint}`);
        break;
    } catch (error) {
        console.log(`失败的端点: ${endpoint}`);
    }
}
```

## 错误排查

### 常见错误及解决方案

#### 1. 认证失败 (401 Unauthorized)

```
错误: HTTP 401: Unauthorized
解决: 检查sessionToken和userId是否有效
```

#### 2. 资源不存在 (404 Not Found)

```
错误: HTTP 404: Not Found
解决: 
- 检查Story ID是否正确
- 确认API端点路径
```

#### 3. 服务器错误 (500 Internal Server Error)

```
错误: HTTP 500: Internal Server Error
解决:
- 检查请求参数格式
- 确认服务器状态
```

#### 4. 网络连接问题

```
错误: 请求失败: ENOTFOUND
解决:
- 检查网络连接
- 确认服务器地址
- 检查防火墙设置
```

### 调试技巧

#### 启用详细日志

```javascript
const simulator = new StoryResourceSimulator({
    debug: true
});
```

#### 添加请求拦截

```javascript
// 在story_resource_simulator.js中添加
console.log('请求URL:', url.toString());
console.log('请求头:', options.headers);
console.log('请求体:', postData);
```

## 高级用法

### 自定义请求处理

```javascript
class CustomStorySimulator extends StoryResourceSimulator {
    async _makeRequest(params) {
        // 添加自定义逻辑
        console.log('发送请求:', params);
        
        const result = await super._makeRequest(params);
        
        // 添加响应处理
        console.log('收到响应:', result);
        
        return result;
    }
}
```

### 批量处理大量Story

```javascript
async function getAllStories(startId, endId) {
    const simulator = new StoryResourceSimulator();
    const results = [];
    
    for (let id = startId; id <= endId; id++) {
        try {
            const story = await simulator.getStoryResource([id.toString()], 0);
            results.push({ id, data: story });
            
            // 添加延迟避免请求过快
            await new Promise(resolve => setTimeout(resolve, 1000));
        } catch (error) {
            console.log(`Story ${id} 获取失败:`, error.message);
        }
    }
    
    return results;
}
```

### 缓存机制

```javascript
class CachedStorySimulator extends StoryResourceSimulator {
    constructor(options) {
        super(options);
        this.cache = new Map();
    }
    
    async getStoryResource(storyIds, adult = 0) {
        const cacheKey = `${storyIds.join(',')}_${adult}`;
        
        if (this.cache.has(cacheKey)) {
            console.log('从缓存返回:', cacheKey);
            return this.cache.get(cacheKey);
        }
        
        const result = await super.getStoryResource(storyIds, adult);
        this.cache.set(cacheKey, result);
        
        return result;
    }
}
```

## 性能优化

### 1. 连接池

```javascript
const https = require('https');

const agent = new https.Agent({
    keepAlive: true,
    maxSockets: 5
});

// 在请求选项中使用
const options = {
    // ... 其他选项
    agent: agent
};
```

### 2. 请求限流

```javascript
class RateLimitedSimulator extends StoryResourceSimulator {
    constructor(options) {
        super(options);
        this.requestQueue = [];
        this.isProcessing = false;
        this.requestDelay = options.requestDelay || 1000;
    }
    
    async getStoryResource(storyIds, adult = 0) {
        return new Promise((resolve, reject) => {
            this.requestQueue.push({ storyIds, adult, resolve, reject });
            this.processQueue();
        });
    }
    
    async processQueue() {
        if (this.isProcessing || this.requestQueue.length === 0) {
            return;
        }
        
        this.isProcessing = true;
        
        while (this.requestQueue.length > 0) {
            const { storyIds, adult, resolve, reject } = this.requestQueue.shift();
            
            try {
                const result = await super.getStoryResource(storyIds, adult);
                resolve(result);
            } catch (error) {
                reject(error);
            }
            
            if (this.requestQueue.length > 0) {
                await new Promise(resolve => setTimeout(resolve, this.requestDelay));
            }
        }
        
        this.isProcessing = false;
    }
}
```

## 法律和道德考虑

1. **遵守服务条款**: 确保你的使用符合游戏的服务条款
2. **合理使用**: 不要发送过多请求导致服务器过载
3. **数据保护**: 妥善保管获取的数据，不要泄露或滥用
4. **知识产权**: 尊重游戏内容的版权和知识产权
