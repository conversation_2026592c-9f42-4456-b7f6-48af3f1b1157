# 深海传说 Story资源获取模拟器

这个工具基于深海传说游戏客户端的 `getStoryResource` 函数，可以在不启动游戏的情况下获取Story的JSON文件。

## 客户端源码分析

从 `@client/1.273.0/src/project.js` 中找到的关键函数：

```javascript
getStoryResource:function(t,e,i){
    var a={storyIds:t.join(","),adult:e||0};
    this._requestCallApi(n.STORY_GET_RESOURCE,a,function(t,e){
        return i(t?void 0:e)
    }
}
```

其中：
- `t` = storyIds数组
- `e` = adult标志 (0或1)
- `i` = 回调函数
- `n.STORY_GET_RESOURCE` = "getResource"

## 安装和使用

### 1. 安装依赖

```bash
npm install
```

### 2. 基本使用

```javascript
const StoryResourceSimulator = require('./story_resource_simulator');

// 创建模拟器实例
const simulator = new StoryResourceSimulator({
    baseUrl: 'https://tonofura-w-cdn-client.deepone-online.com',
    apiPath: '/api/story/getResource', // 可能需要调整
    headers: {
        'X-Requested-With': 'XMLHttpRequest'
    }
});

// 获取Story资源
async function getStory() {
    try {
        const storyData = await simulator.getStoryResource(['1001'], 0);
        console.log(storyData);
    } catch (error) {
        console.error('获取失败:', error.message);
    }
}

getStory();
```

### 3. 高级配置

```javascript
// 带认证的配置
const simulator = new StoryResourceSimulator({
    baseUrl: 'https://your-game-server.com',
    apiPath: '/api/story/getResource',
    sessionToken: 'your_session_token',
    userId: 'your_user_id',
    headers: {
        'Authorization': 'Bearer your_token',
        'X-Game-Version': '1.273.0'
    }
});
```

## API 参考

### `getStoryResource(storyIds, adult)`

获取指定Story ID的资源数据。

**参数：**
- `storyIds` (Array): Story ID数组，例如 `['1001', '1002']`
- `adult` (Number): 成人内容标志，0或1，默认为0

**返回：**
- Promise<Object>: Story资源数据

### `getMultipleStoryResources(storyIdGroups, adult)`

批量获取多组Story资源。

**参数：**
- `storyIdGroups` (Array<Array>): Story ID组数组
- `adult` (Number): 成人内容标志

**返回：**
- Promise<Array>: Story资源数据数组

## 注意事项

### 1. 服务器地址配置

需要根据实际的游戏服务器配置正确的 `baseUrl` 和 `apiPath`。可能的路径包括：
- `/api/story/getResource`
- `/story/getResource`
- `/api/v1/story/getResource`

### 2. 认证要求

大多数游戏API需要有效的用户认证。你可能需要：
- 有效的会话令牌 (session token)
- 用户ID
- 游戏版本号
- 设备ID等

### 3. 请求头配置

确保请求头模拟真实的客户端：
```javascript
headers: {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    'Content-Type': 'application/x-www-form-urlencoded',
    'Accept': 'application/json',
    'X-Requested-With': 'XMLHttpRequest',
    'X-Game-Version': '1.273.0'
}
```

### 4. 错误处理

常见错误类型：
- 认证失败 (401)
- Story ID不存在 (404)
- 服务器错误 (500)
- 网络连接问题

## 调试技巧

### 1. 抓包分析

使用工具如 Fiddler 或 Charles 抓取真实客户端的请求：
- 观察实际的API端点
- 分析请求头和参数格式
- 获取有效的认证信息

### 2. 浏览器开发者工具

在游戏网页版中：
1. 打开开发者工具 (F12)
2. 切换到 Network 标签
3. 触发Story相关操作
4. 查看实际的API调用

### 3. 日志输出

启用详细日志：
```javascript
const simulator = new StoryResourceSimulator({
    debug: true // 启用调试模式
});
```

## 示例用法

```javascript
// 获取单个Story
const story1 = await simulator.getStoryResource(['1001'], 0);

// 获取多个Story
const stories = await simulator.getStoryResource(['1001', '1002', '1003'], 0);

// 批量获取
const batchStories = await simulator.getMultipleStoryResources([
    ['1001'],
    ['1002', '1003'],
    ['1004', '1005', '1006']
], 0);
```

## 法律声明

此工具仅用于学习和研究目的。请确保：
1. 遵守游戏的服务条款
2. 不要进行恶意请求或攻击
3. 尊重游戏开发商的知识产权
4. 合理使用，避免对服务器造成过大负载
