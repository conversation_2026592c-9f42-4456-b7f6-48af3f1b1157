/**
 * 深海传说 (Deep One) 客户端Story资源获取模拟器
 * 
 * 基于客户端源码中的getStoryResource函数实现
 * 原函数: getStoryResource:function(t,e,i){var a={storyIds:t.join(","),adult:e||0};this._requestCallApi(n.STORY_GET_RESOURCE,a,function(t,e){return i(t?void 0:e)}
 */

const https = require('https');
const http = require('http');
const querystring = require('querystring');

class StoryResourceSimulator {
    constructor(options = {}) {
        // 游戏服务器配置 - 需要根据实际情况调整
        this.baseUrl = options.baseUrl || 'https://tonofura-w-cdn-client.deepone-online.com';
        this.apiPath = options.apiPath || '/api/story/getResource'; // 根据STORY_GET_RESOURCE:"getResource"推测
        
        // 请求头配置 - 模拟客户端
        this.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'ja-JP,ja;q=0.9,en;q=0.8',
            'Referer': this.baseUrl,
            ...options.headers
        };
        
        // 认证信息 - 需要有效的用户会话
        this.sessionToken = options.sessionToken || null;
        this.userId = options.userId || null;
    }

    /**
     * 模拟客户端的getStoryResource函数
     * @param {Array<string|number>} storyIds - Story ID数组
     * @param {number} adult - 成人内容标志 (0或1)
     * @returns {Promise<Object>} Story资源数据
     */
    async getStoryResource(storyIds, adult = 0) {
        // 验证参数
        if (!Array.isArray(storyIds) || storyIds.length === 0) {
            throw new Error('storyIds must be a non-empty array');
        }

        // 构建请求参数 - 模拟客户端的参数格式
        const params = {
            storyIds: storyIds.join(','),
            adult: adult || 0
        };

        // 如果有认证信息，添加到参数中
        if (this.sessionToken) {
            params.sessionToken = this.sessionToken;
        }
        if (this.userId) {
            params.userId = this.userId;
        }

        try {
            const response = await this._makeRequest(params);
            return response;
        } catch (error) {
            console.error('获取Story资源失败:', error.message);
            throw error;
        }
    }

    /**
     * 发送HTTP请求
     * @param {Object} params - 请求参数
     * @returns {Promise<Object>} 响应数据
     */
    _makeRequest(params) {
        return new Promise((resolve, reject) => {
            const postData = querystring.stringify(params);
            
            const url = new URL(this.apiPath, this.baseUrl);
            const isHttps = url.protocol === 'https:';
            const httpModule = isHttps ? https : http;
            
            const options = {
                hostname: url.hostname,
                port: url.port || (isHttps ? 443 : 80),
                path: url.pathname + url.search,
                method: 'POST',
                headers: {
                    ...this.headers,
                    'Content-Length': Buffer.byteLength(postData)
                }
            };

            const req = httpModule.request(options, (res) => {
                let data = '';
                
                res.on('data', (chunk) => {
                    data += chunk;
                });
                
                res.on('end', () => {
                    try {
                        const jsonData = JSON.parse(data);
                        
                        if (res.statusCode === 200) {
                            resolve(jsonData);
                        } else {
                            reject(new Error(`HTTP ${res.statusCode}: ${jsonData.message || data}`));
                        }
                    } catch (parseError) {
                        reject(new Error(`JSON解析失败: ${parseError.message}`));
                    }
                });
            });

            req.on('error', (error) => {
                reject(new Error(`请求失败: ${error.message}`));
            });

            req.write(postData);
            req.end();
        });
    }

    /**
     * 批量获取多个Story资源
     * @param {Array<Array>} storyIdGroups - Story ID组数组
     * @param {number} adult - 成人内容标志
     * @returns {Promise<Array>} Story资源数据数组
     */
    async getMultipleStoryResources(storyIdGroups, adult = 0) {
        const promises = storyIdGroups.map(group => 
            this.getStoryResource(group, adult)
        );
        
        try {
            return await Promise.all(promises);
        } catch (error) {
            console.error('批量获取Story资源失败:', error.message);
            throw error;
        }
    }

    /**
     * 设置认证信息
     * @param {string} sessionToken - 会话令牌
     * @param {string} userId - 用户ID
     */
    setAuth(sessionToken, userId) {
        this.sessionToken = sessionToken;
        this.userId = userId;
    }
}

// 使用示例
async function example() {
    // 创建模拟器实例
    const simulator = new StoryResourceSimulator({
        // 根据实际情况配置服务器地址
        baseUrl: 'https://tonofura-w-cdn-client.deepone-online.com',
        // 可能需要的认证头
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    });

    // 设置认证信息（如果需要）
    // simulator.setAuth('your_session_token', 'your_user_id');

    try {
        // 获取单个Story资源
        const storyData = await simulator.getStoryResource(['1001'], 0);
        console.log('Story资源:', JSON.stringify(storyData, null, 2));

        // 批量获取多个Story资源
        const multipleStories = await simulator.getMultipleStoryResources([
            ['1001'],
            ['1002', '1003'],
            ['1004']
        ], 0);
        console.log('批量Story资源:', multipleStories);

    } catch (error) {
        console.error('获取失败:', error.message);
    }
}

module.exports = StoryResourceSimulator;

// 如果直接运行此文件，执行示例
if (require.main === module) {
    example();
}
