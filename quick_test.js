/**
 * 快速测试工具 - 使用最新的认证信息
 * 
 * 使用方法:
 * 1. 从浏览器开发者工具复制Authorization头
 * 2. 运行: node quick_test.js "你的Authorization头内容" "Story ID"
 */

const StoryResourceSimulator = require('./story_resource_simulator');

function parseAuthorizationHeader(authHeader) {
    // 解析Authorization头，提取OAuth参数
    const oauthParams = {};

    // 处理不同的输入格式
    let cleanHeader = authHeader;

    // 如果是完整的请求头格式，提取authorization值
    if (authHeader.includes('authorization:') || authHeader.includes('Authorization:')) {
        const authMatch = authHeader.match(/authorization:\s*(.+)/i);
        if (authMatch) {
            cleanHeader = authMatch[1].trim();
        }
    }

    // 移除 "OAuth realm="Users" " 前缀
    cleanHeader = cleanHeader.replace(/^OAuth\s+realm="[^"]*"\s*/, '');

    // 使用正则表达式提取各个参数，处理可能的转义字符
    const paramRegex = /(\w+)=\\?"([^"\\]*(?:\\.[^"\\]*)*)\\?"/g;
    let match;

    while ((match = paramRegex.exec(cleanHeader)) !== null) {
        // 移除转义字符
        const value = match[2].replace(/\\"/g, '"').replace(/\\\\/g, '\\');
        oauthParams[match[1]] = value;
    }

    return oauthParams;
}

async function quickTest(authHeader, storyId = '100503') {
    console.log('=== 快速测试工具 ===\n');
    
    if (!authHeader) {
        console.log('❌ 请提供Authorization头信息');
        console.log('\n使用方法:');
        console.log('node quick_test.js "OAuth realm=\\"Users\\" oauth_token=\\"...\\" ..." "100503"');
        console.log('\n如何获取Authorization头:');
        console.log('1. 在浏览器中打开游戏');
        console.log('2. 按F12打开开发者工具');
        console.log('3. 切换到Network标签');
        console.log('4. 在游戏中查看Story');
        console.log('5. 找到 /deep-one/api/story/getResource 请求');
        console.log('6. 复制请求头中的 authorization 值');
        return;
    }

    try {
        // 解析Authorization头
        console.log('正在解析Authorization头...');
        const oauthParams = parseAuthorizationHeader(authHeader);
        
        console.log('解析到的OAuth参数:');
        Object.entries(oauthParams).forEach(([key, value]) => {
            if (key === 'oauth_signature') {
                console.log(`  ${key}: ${value.substring(0, 10)}...`);
            } else {
                console.log(`  ${key}: ${value}`);
            }
        });
        console.log('');

        // 创建模拟器
        const simulator = new StoryResourceSimulator({
            baseUrl: 'https://tonofura-web-w.deepone-online.com',
            apiPath: '/deep-one/api/story/getResource',
            oauthConfig: oauthParams
        });

        console.log(`正在获取Story ID: ${storyId}`);
        console.log('---');

        const startTime = Date.now();
        const storyData = await simulator.getStoryResource([storyId], 0);
        const endTime = Date.now();

        console.log(`\n✅ 成功获取Story资源！耗时: ${endTime - startTime}ms`);
        console.log('\nStory数据结构:');
        
        // 显示数据的基本结构，不显示全部内容
        if (typeof storyData === 'object') {
            console.log('数据类型: Object');
            console.log('主要字段:', Object.keys(storyData));
            
            // 如果有data字段，显示其结构
            if (storyData.data) {
                console.log('data字段类型:', typeof storyData.data);
                if (typeof storyData.data === 'object') {
                    console.log('data字段内容:', Object.keys(storyData.data));
                }
            }
        } else {
            console.log('数据类型:', typeof storyData);
        }

        // 保存完整数据到文件
        const fs = require('fs');
        const filename = `story_${storyId}_${Date.now()}.json`;
        fs.writeFileSync(filename, JSON.stringify(storyData, null, 2));
        console.log(`\n📁 完整数据已保存到: ${filename}`);

        // 显示部分数据内容（前500字符）
        const dataStr = JSON.stringify(storyData, null, 2);
        if (dataStr.length > 500) {
            console.log('\n📄 数据预览 (前500字符):');
            console.log(dataStr.substring(0, 500) + '...');
        } else {
            console.log('\n📄 完整数据:');
            console.log(dataStr);
        }

    } catch (error) {
        console.log('❌ 测试失败:', error.message);
        
        if (error.message.includes('401')) {
            console.log('\n🔍 认证失败的可能原因:');
            console.log('- OAuth签名已过期（通常几分钟内就会过期）');
            console.log('- oauth_token已失效');
            console.log('- 时间戳不匹配');
            console.log('\n💡 解决方案:');
            console.log('- 重新从浏览器获取最新的Authorization头');
            console.log('- 确保在获取后立即使用');
        } else if (error.message.includes('403')) {
            console.log('\n🔍 权限不足的可能原因:');
            console.log('- Story ID不存在或无权访问');
            console.log('- 账号权限不足');
        } else if (error.message.includes('404')) {
            console.log('\n🔍 资源不存在的可能原因:');
            console.log('- Story ID错误');
            console.log('- API端点路径变更');
        }
    }
}

// 命令行参数处理
function main() {
    const args = process.argv.slice(2);
    
    if (args.length === 0) {
        console.log('快速测试工具 - 深海传说Story资源获取\n');
        console.log('使用方法:');
        console.log('  node quick_test.js "Authorization头内容" [Story ID]');
        console.log('');
        console.log('示例:');
        console.log('  node quick_test.js "OAuth realm=\\"Users\\" oauth_token=\\"abc123\\" ..." "100503"');
        console.log('');
        console.log('获取Authorization头的步骤:');
        console.log('1. 在浏览器中打开深海传说游戏');
        console.log('2. 按F12打开开发者工具');
        console.log('3. 切换到Network标签');
        console.log('4. 在游戏中查看任意Story');
        console.log('5. 找到对 /deep-one/api/story/getResource 的请求');
        console.log('6. 点击该请求，查看Request Headers');
        console.log('7. 复制 authorization 字段的完整值');
        console.log('8. 粘贴到此命令中（记得加引号）');
        return;
    }

    const authHeader = args[0];
    const storyId = args[1] || '100503';
    
    quickTest(authHeader, storyId).catch(console.error);
}

if (require.main === module) {
    main();
}

module.exports = { quickTest, parseAuthorizationHeader };
