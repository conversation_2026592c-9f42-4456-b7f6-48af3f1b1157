# 深海传说认证信息配置文件
# 
# 使用方法:
# 1. 将此文件重命名为 auth.txt
# 2. 将下面的示例内容替换为你从浏览器获取的真实authorization头
# 3. 保存文件后运行 node easy_test.js

# 方式1: 直接粘贴浏览器开发者工具中的authorization字段值
# 例如:
authorization: OAuth realm="Users" oauth_token="2qqR9jHrECutDsVMjLbBHa" xoauth_requestor_id="7nkQlgizFfm3Z1om7Qsnmf" oauth_consumer_key="*********************************" oauth_signature_method="HMAC-SHA256" oauth_nonce="8351254459087218" oauth_timestamp="1755924912" oauth_signature="8QbB0bmRRFbaZz+F38FL65ZoEuIx3eIYnqxwvl346mQ="

# 方式2: 或者只粘贴OAuth部分
# OAuth realm="Users" oauth_token="你的token" xoauth_requestor_id="你的requestor_id" oauth_consumer_key="*********************************" oauth_signature_method="HMAC-SHA256" oauth_nonce="你的nonce" oauth_timestamp="你的timestamp" oauth_signature="你的signature"

# 方式3: 甚至可以分多行写（程序会自动合并）
# OAuth realm="Users" 
# oauth_token="你的token" 
# xoauth_requestor_id="你的requestor_id" 
# oauth_consumer_key="*********************************" 
# oauth_signature_method="HMAC-SHA256" 
# oauth_nonce="你的nonce" 
# oauth_timestamp="你的timestamp" 
# oauth_signature="你的signature"

# 注意事项:
# - 以 # 开头的行是注释，会被忽略
# - OAuth签名通常几分钟内就会过期，需要及时更新
# - 可以保留多个版本，程序会使用最后一个有效的
