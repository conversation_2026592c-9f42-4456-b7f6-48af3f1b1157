/**
 * 使用真实请求参数测试Story资源获取
 */

const StoryResourceSimulator = require('./story_resource_simulator');

async function testRealRequest() {
    console.log('=== 使用真实请求参数测试 ===\n');

    // 基于你提供的真实请求头创建模拟器
    const simulator = new StoryResourceSimulator({
        baseUrl: 'https://tonofura-web-w.deepone-online.com',
        apiPath: '/deep-one/api/story/getResource',
        
        // OAuth认证配置 - 基于你提供的请求头
        oauthConfig: {
            oauth_token: '2qqR9jHrECutDsVMjLbBHa',
            xoauth_requestor_id: '7nkQlgizFfm3Z1om7Qsnmf',
            oauth_consumer_key: '*********************************',
            oauth_signature_method: 'HMAC-SHA256',
            // 注意：oauth_signature需要动态生成，这里使用你提供的示例
            oauth_signature: '8QbB0bmRRFbaZz+F38FL65ZoEuIx3eIYnqxwvl346mQ='
        }
    });

    try {
        console.log('正在获取Story ID: 100503');
        console.log('服务器: https://tonofura-web-w.deepone-online.com');
        console.log('API路径: /deep-one/api/story/getResource');
        console.log('---');

        const startTime = Date.now();
        
        // 使用你提供的Story ID
        const storyData = await simulator.getStoryResource(['100503'], 0);
        
        const endTime = Date.now();
        
        console.log(`\n✅ 请求成功！耗时: ${endTime - startTime}ms`);
        console.log('Story资源数据:');
        console.log(JSON.stringify(storyData, null, 2));
        
        // 保存到文件
        const fs = require('fs');
        const filename = `story_100503_${Date.now()}.json`;
        fs.writeFileSync(filename, JSON.stringify(storyData, null, 2));
        console.log(`\n📁 数据已保存到: ${filename}`);

    } catch (error) {
        console.log('❌ 请求失败:', error.message);
        
        // 分析可能的错误原因
        if (error.message.includes('401')) {
            console.log('\n🔍 可能的原因:');
            console.log('- OAuth签名已过期或无效');
            console.log('- oauth_token已失效');
            console.log('- 需要重新获取认证信息');
        } else if (error.message.includes('403')) {
            console.log('\n🔍 可能的原因:');
            console.log('- 权限不足');
            console.log('- Story ID不存在或无权访问');
        } else if (error.message.includes('404')) {
            console.log('\n🔍 可能的原因:');
            console.log('- API端点路径错误');
            console.log('- Story ID不存在');
        } else if (error.message.includes('ENOTFOUND')) {
            console.log('\n🔍 可能的原因:');
            console.log('- 网络连接问题');
            console.log('- 服务器地址错误');
        }
        
        console.log('\n💡 建议:');
        console.log('1. 检查网络连接');
        console.log('2. 确认OAuth认证信息是否有效');
        console.log('3. 尝试从浏览器重新获取最新的请求头');
        console.log('4. 检查Story ID是否正确');
    }
}

// 测试不同的Story ID
async function testMultipleStories() {
    console.log('\n=== 测试多个Story ID ===\n');
    
    const simulator = new StoryResourceSimulator({
        baseUrl: 'https://tonofura-web-w.deepone-online.com',
        apiPath: '/deep-one/api/story/getResource',
        oauthConfig: {
            oauth_token: '2qqR9jHrECutDsVMjLbBHa',
            xoauth_requestor_id: '7nkQlgizFfm3Z1om7Qsnmf',
            oauth_consumer_key: '*********************************',
            oauth_signature_method: 'HMAC-SHA256',
            oauth_signature: '8QbB0bmRRFbaZz+F38FL65ZoEuIx3eIYnqxwvl346mQ='
        }
    });

    const testStoryIds = ['100503', '100501', '100502']; // 可以尝试不同的ID
    
    for (const storyId of testStoryIds) {
        try {
            console.log(`正在测试Story ID: ${storyId}`);
            const result = await simulator.getStoryResource([storyId], 0);
            console.log(`✅ Story ${storyId} 获取成功`);
            
            // 添加延迟避免请求过快
            await new Promise(resolve => setTimeout(resolve, 1000));
            
        } catch (error) {
            console.log(`❌ Story ${storyId} 获取失败: ${error.message}`);
        }
    }
}

// 显示如何获取新的认证信息
function showHowToGetAuth() {
    console.log('\n=== 如何获取最新的认证信息 ===\n');
    console.log('1. 在浏览器中打开游戏');
    console.log('2. 按F12打开开发者工具');
    console.log('3. 切换到Network标签');
    console.log('4. 在游戏中触发Story相关操作');
    console.log('5. 找到对 /deep-one/api/story/getResource 的请求');
    console.log('6. 复制请求头中的Authorization信息');
    console.log('7. 更新代码中的oauthConfig参数');
    console.log('\n需要的参数:');
    console.log('- oauth_token');
    console.log('- xoauth_requestor_id');
    console.log('- oauth_signature (每次请求都不同)');
    console.log('- oauth_nonce (每次请求都不同)');
    console.log('- oauth_timestamp (每次请求都不同)');
}

// 主函数
async function main() {
    await testRealRequest();
    
    // 如果第一个测试失败，显示帮助信息
    showHowToGetAuth();
    
    // 可以取消注释来测试多个Story ID
    // await testMultipleStories();
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = { testRealRequest, testMultipleStories, showHowToGetAuth };
