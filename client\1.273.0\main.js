window.boot = function() {
	var settings = window._CCSettings;
	window._CCSettings = undefined;

	if (!settings.debug) {
		var uuids = settings.uuids;

		var rawAssets = settings.rawAssets;
		var assetTypes = settings.assetTypes;
		var realRawAssets = (settings.rawAssets = {});
		for (var mount in rawAssets) {
			var entries = rawAssets[mount];
			var realEntries = (realRawAssets[mount] = {});
			for (var id in entries) {
				var entry = entries[id];
				var type = entry[1];
				// retrieve minified raw asset
				if (typeof type === 'number') {
					entry[1] = assetTypes[type];
				}
				// retrieve uuid
				realEntries[uuids[id] || id] = entry;
			}
		}

		var scenes = settings.scenes;
		for (var i = 0; i < scenes.length; ++i) {
			var scene = scenes[i];
			if (typeof scene.uuid === 'number') {
				scene.uuid = uuids[scene.uuid];
			}
		}

		var packedAssets = settings.packedAssets;
		for (var packId in packedAssets) {
			var packedIds = packedAssets[packId];
			for (var j = 0; j < packedIds.length; ++j) {
				if (typeof packedIds[j] === 'number') {
					packedIds[j] = uuids[packedIds[j]];
				}
			}
		}

		var subpackages = settings.subpackages;
		for (var subId in subpackages) {
			var uuidArray = subpackages[subId].uuids;
			if (uuidArray) {
				for (var k = 0, l = uuidArray.length; k < l; k++) {
					if (typeof uuidArray[k] === 'number') {
						uuidArray[k] = uuids[uuidArray[k]];
					}
				}
			}
		}
	}

	var onStart = function() {
		cc.loader.downloader._subpackages = settings.subpackages;

		cc.view.enableRetina(true);
		cc.view.resizeWithBrowserSize(true);

		cc.view.enableAutoFullScreen(false);

		if (cc.sys.isMobile) {
			cc.view.setOrientation(cc.macro.ORIENTATION_LANDSCAPE);
			cc.view.enableAutoFullScreen(
				[
					cc.sys.BROWSER_TYPE_BAIDU,
					cc.sys.BROWSER_TYPE_WECHAT,
					cc.sys.BROWSER_TYPE_MOBILE_QQ,
					cc.sys.BROWSER_TYPE_MIUI
				].indexOf(cc.sys.browserType) < 0
			);
		}

		// if (cc.sys.isMobile) {
		// 	cc.view.setOrientation(cc.macro.ORIENTATION_LANDSCAPE);
		//
		// 	cc.view.on('design-resolution-changed', function() {
		// 		window.parent.postMessage(
		// 			{
		// 				api: 'setIframeHeightDmm',
		// 				param: {}
		// 			},
		// 			'*'
		// 		);
		// 	});
		// }

		// Limit downloading max concurrent task to 2,
		// more tasks simultaneously may cause performance draw back on some android system / browsers.
		// You can adjust the number based on your own test result, you have to set it before any loading process to take effect.
		if (cc.sys.isBrowser && cc.sys.os === cc.sys.OS_ANDROID) {
			cc.macro.DOWNLOAD_MAX_CONCURRENT = 2;
		}

		function loadScene(launchScene) {
			cc.director.loadScene(launchScene, function(err) {
				if (!err) {
					if (cc.sys.isBrowser) {
						// show canvas
						var canvas = document.getElementById('GameCanvas');
						canvas.style.visibility = '';
						var div = document.getElementById('GameDiv');
						if (div) {
							div.style.backgroundImage = '';
						}
					}
					cc.loader.onProgress = null;
					cc.log('Success to load scene: ' + launchScene);
				} else if (CC_BUILD) {
					setTimeout(function() {
						loadScene(launchScene);
					}, 1000);
				}
			});
		}

		var launchScene = settings.launchScene;

		// load scene
		loadScene(launchScene);
	};

	// jsList
	var jsList = settings.jsList;

	var bundledScript = settings.debug ? 'src/project.dev.js' : 'src/project.js';
	if (jsList) {
		jsList = jsList.map(function(x) {
			return 'src/' + x;
		});
		jsList.push(bundledScript);
	} else {
		jsList = [bundledScript];
	}

	var option = {
		id: 'GameCanvas',
		scenes: settings.scenes,
		debugMode: settings.debug ? cc.debug.DebugMode.INFO : cc.debug.DebugMode.ERROR,
		showFPS: settings.debug,
		frameRate: 60,
		jsList: jsList,
		groupList: settings.groupList,
		collisionMatrix: settings.collisionMatrix
	};

	// init assets
	cc.AssetLibrary.init({
		libraryPath: 'res/import',
		rawAssetsBase: 'res/raw-',
		rawAssets: settings.rawAssets,
		packedAssets: settings.packedAssets,
		md5AssetsMap: settings.md5AssetsMap,
		subpackages: settings.subpackages
	});

	// Cocos背景透過設定を有効化
	cc.macro.ENABLE_TRANSPARENT_CANVAS = true;

	// iOS 14 動作鈍化解消対応
	var isIOS14Device =
		cc.sys.os === cc.sys.OS_IOS &&
		cc.sys.isBrowser &&
		cc.sys.isMobile &&
		/iPhone OS 14/.test(window.navigator.userAgent);
	if (isIOS14Device) {
		cc.MeshBuffer.prototype.checkAndSwitchBuffer = function(vertexCount) {
			if (this.vertexOffset + vertexCount > 65535) {
				this.uploadData();
				this._batcher._flush();
			}
		};
		cc.MeshBuffer.prototype.forwardIndiceStartToOffset = function() {
			this.uploadData();
			this.switchBuffer();
		};
	}

	cc.game.run(option, onStart);
};

if (window.jsb) {
	var isRuntime = typeof loadRuntime === 'function';
	if (isRuntime) {
		require('src/settings.js');
		require('src/cocos2d-runtime.js');
		require('jsb-adapter/engine/index.js');
	} else {
		require('src/settings.js');
		require('src/cocos2d-jsb.js');
		require('jsb-adapter/jsb-engine.js');
	}

	cc.macro.CLEANUP_IMAGE_CACHE = true;
	window.boot();
}
