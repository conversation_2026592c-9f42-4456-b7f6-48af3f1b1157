{"name": "deepone-story-resource-simulator", "version": "1.0.0", "description": "深海传说游戏Story资源获取模拟器", "main": "story_resource_simulator.js", "bin": {"deepone-story": "./cli.js"}, "scripts": {"start": "node cli.js", "test": "node test.js", "example": "node example.js"}, "keywords": ["deepone", "game", "story", "resource", "simulator", "api"], "author": "Your Name", "license": "MIT", "engines": {"node": ">=12.0.0"}, "dependencies": {}, "devDependencies": {}, "repository": {"type": "git", "url": "https://github.com/yourusername/deepone-story-simulator.git"}, "bugs": {"url": "https://github.com/yourusername/deepone-story-simulator/issues"}, "homepage": "https://github.com/yourusername/deepone-story-simulator#readme"}