# 深海传说 Story获取工具 - 简单使用说明

## 🎯 解决了引号转义问题！

现在你有**4种超简单的方法**来使用这个工具，完全不需要手动处理引号转义！

## 📋 获取认证信息（一次性操作）

1. 在浏览器中打开深海传说游戏
2. 按 `F12` 打开开发者工具
3. 点击 "Network"（网络）标签
4. 在游戏中查看任意Story
5. 找到 `/deep-one/api/story/getResource` 请求
6. 点击该请求，查看 "Request Headers"
7. **直接复制** `authorization` 字段的完整值（不用管引号）

## 🚀 使用方法

### 方法1: 文件方式（推荐）

```bash
# 1. 编辑认证文件
notepad auth.txt

# 2. 将复制的authorization头直接粘贴到文件中，保存

# 3. 运行（Windows）
run.bat 100503

# 或者（所有系统）
node easy_test.js 100503
```

### 方法2: 交互式输入

```bash
# 运行后按提示粘贴
node easy_test.js 100503

# 程序会提示你粘贴authorization头，直接粘贴即可
```

### 方法3: 命令行（现在支持各种格式）

```bash
# 直接粘贴，无需处理引号
node easy_test.js "你复制的完整authorization头" 100503
```

### 方法4: 环境变量

```bash
# Windows
set DEEPONE_AUTH=你复制的authorization头
node easy_test.js 100503

# Linux/Mac
export DEEPONE_AUTH="你复制的authorization头"
node easy_test.js 100503
```

## ✨ 支持的输入格式

程序会自动识别和处理以下所有格式：

```
✅ 浏览器原始格式:
authorization: OAuth realm="Users" oauth_token="abc123" ...

✅ 纯OAuth字符串:
OAuth realm="Users" oauth_token="abc123" ...

✅ cURL格式:
-H "Authorization: OAuth realm="Users" oauth_token="abc123" ..."

✅ 多行格式:
OAuth realm="Users" 
oauth_token="abc123" 
oauth_signature="xyz789"

✅ 带转义字符的格式:
OAuth realm=\"Users\" oauth_token=\"abc123\" ...
```

## 📁 输出结果

成功后会生成：
- `story_[ID]_[时间戳].json` - 完整的Story数据

## 🔧 常见问题

### Q: 还是提示401错误？
A: OAuth签名会很快过期，重新从浏览器获取最新的authorization头即可。

### Q: 如何获取多个Story？
A: 
```bash
# 方法1: 多次运行
node easy_test.js 100501
node easy_test.js 100502
node easy_test.js 100503

# 方法2: 使用批处理
for /L %i in (100501,1,100510) do node easy_test.js %i
```

### Q: 如何知道有哪些Story ID？
A: 可以尝试常见的ID模式：
- 100501, 100502, 100503... (主线Story)
- 200001, 200002... (活动Story)
- 从游戏中观察URL或请求来确定

## 🎉 成功示例

当一切正常时，你会看到：

```
=== 简易测试工具 ===

正在尝试获取认证信息...
✓ 从 auth.txt 文件读取

解析到的OAuth参数:
  oauth_token: 2qqR9jHrECutDsVMjLbBHa
  xoauth_requestor_id: 7nkQlgizFfm3Z1om7Qsnmf
  ...

正在获取Story ID: 100503...
✅ 成功！耗时: 1234ms
📁 数据已保存到: story_100503_1234567890.json

📊 数据摘要:
- 数据类型: Object
- 主要字段: success, data, message
- JSON大小: 5678 字符
```

## 💡 小贴士

1. **第一次使用**: 运行 `node easy_test.js` 会自动创建示例文件
2. **批量获取**: 可以写简单的脚本循环获取多个Story
3. **数据分析**: 获取的JSON文件包含Story的完整信息，可以用于分析
4. **备份认证**: 可以在 `auth.txt` 中保存多个认证信息，程序会使用最新的

现在完全不需要担心引号转义问题了！🎉
