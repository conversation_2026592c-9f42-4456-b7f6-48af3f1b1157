/**
 * 深海传说 Story资源获取模拟器测试
 */

const StoryResourceSimulator = require('./story_resource_simulator');

// 简单的测试框架
class SimpleTest {
    constructor() {
        this.tests = [];
        this.passed = 0;
        this.failed = 0;
    }

    test(name, fn) {
        this.tests.push({ name, fn });
    }

    async run() {
        console.log('=== 运行测试 ===\n');
        
        for (const test of this.tests) {
            try {
                console.log(`运行测试: ${test.name}`);
                await test.fn();
                console.log('✅ 通过\n');
                this.passed++;
            } catch (error) {
                console.log(`❌ 失败: ${error.message}\n`);
                this.failed++;
            }
        }

        console.log('=== 测试结果 ===');
        console.log(`通过: ${this.passed}`);
        console.log(`失败: ${this.failed}`);
        console.log(`总计: ${this.tests.length}`);
    }
}

// 创建测试实例
const test = new SimpleTest();

// 测试1: 构造函数
test.test('构造函数应该正确初始化', () => {
    const simulator = new StoryResourceSimulator();
    
    if (!simulator.baseUrl) {
        throw new Error('baseUrl未正确初始化');
    }
    
    if (!simulator.headers) {
        throw new Error('headers未正确初始化');
    }
});

// 测试2: 参数验证
test.test('getStoryResource应该验证参数', async () => {
    const simulator = new StoryResourceSimulator();
    
    try {
        await simulator.getStoryResource([], 0);
        throw new Error('应该抛出错误');
    } catch (error) {
        if (!error.message.includes('non-empty array')) {
            throw new Error('错误消息不正确');
        }
    }
    
    try {
        await simulator.getStoryResource(null, 0);
        throw new Error('应该抛出错误');
    } catch (error) {
        if (!error.message.includes('non-empty array')) {
            throw new Error('错误消息不正确');
        }
    }
});

// 测试3: 认证设置
test.test('setAuth应该正确设置认证信息', () => {
    const simulator = new StoryResourceSimulator();
    
    simulator.setAuth('test_token', 'test_user');
    
    if (simulator.sessionToken !== 'test_token') {
        throw new Error('sessionToken设置失败');
    }
    
    if (simulator.userId !== 'test_user') {
        throw new Error('userId设置失败');
    }
});

// 测试4: 配置选项
test.test('构造函数应该接受配置选项', () => {
    const options = {
        baseUrl: 'https://test.example.com',
        apiPath: '/test/api',
        headers: {
            'X-Test': 'test-value'
        }
    };
    
    const simulator = new StoryResourceSimulator(options);
    
    if (simulator.baseUrl !== options.baseUrl) {
        throw new Error('baseUrl配置失败');
    }
    
    if (simulator.apiPath !== options.apiPath) {
        throw new Error('apiPath配置失败');
    }
    
    if (simulator.headers['X-Test'] !== 'test-value') {
        throw new Error('自定义headers配置失败');
    }
});

// 测试5: 批量请求参数验证
test.test('getMultipleStoryResources应该验证参数', async () => {
    const simulator = new StoryResourceSimulator();
    
    try {
        await simulator.getMultipleStoryResources([], 0);
        // 空数组应该成功，返回空结果
    } catch (error) {
        throw new Error('空数组应该被允许');
    }
    
    try {
        await simulator.getMultipleStoryResources([['1001'], []], 0);
        throw new Error('应该抛出错误');
    } catch (error) {
        if (!error.message.includes('non-empty array')) {
            throw new Error('错误消息不正确');
        }
    }
});

// 测试6: URL构建
test.test('应该正确构建请求URL', () => {
    const simulator = new StoryResourceSimulator({
        baseUrl: 'https://example.com',
        apiPath: '/api/test'
    });
    
    // 这里我们测试内部逻辑，实际上需要访问私有方法
    // 在真实项目中，可能需要重构代码以便测试
    const url = new URL(simulator.apiPath, simulator.baseUrl);
    
    if (url.toString() !== 'https://example.com/api/test') {
        throw new Error('URL构建不正确');
    }
});

// 模拟网络请求测试
test.test('网络错误应该被正确处理', async () => {
    const simulator = new StoryResourceSimulator({
        baseUrl: 'https://nonexistent-server-12345.com'
    });
    
    try {
        await simulator.getStoryResource(['1001'], 0);
        throw new Error('应该抛出网络错误');
    } catch (error) {
        if (!error.message.includes('请求失败') && !error.message.includes('ENOTFOUND')) {
            throw new Error('错误类型不正确');
        }
    }
});

// 运行所有测试
if (require.main === module) {
    test.run().catch(console.error);
}

module.exports = test;
