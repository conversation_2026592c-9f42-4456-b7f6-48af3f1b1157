#!/usr/bin/env node

/**
 * 深海传说 Story资源获取命令行工具
 */

const fs = require('fs');
const path = require('path');
const StoryResourceSimulator = require('./story_resource_simulator');

// 读取配置文件
function loadConfig() {
    try {
        const configPath = path.join(__dirname, 'config.json');
        const configData = fs.readFileSync(configPath, 'utf8');
        return JSON.parse(configData);
    } catch (error) {
        console.error('读取配置文件失败:', error.message);
        process.exit(1);
    }
}

// 显示帮助信息
function showHelp() {
    console.log(`
深海传说 Story资源获取工具

用法:
  node cli.js <story_ids> [options]

参数:
  story_ids     Story ID列表，用逗号分隔，例如: 1001,1002,1003

选项:
  --adult       获取成人内容 (默认: 0)
  --output, -o  输出文件路径 (默认: 输出到控制台)
  --config, -c  配置文件路径 (默认: config.json)
  --help, -h    显示帮助信息

示例:
  node cli.js 1001
  node cli.js 1001,1002,1003 --adult 0 --output story_data.json
  node cli.js 1001 --config custom_config.json
`);
}

// 解析命令行参数
function parseArgs() {
    const args = process.argv.slice(2);
    
    if (args.length === 0 || args.includes('--help') || args.includes('-h')) {
        showHelp();
        process.exit(0);
    }

    const storyIds = args[0].split(',').map(id => id.trim());
    const options = {
        adult: 0,
        output: null,
        config: 'config.json'
    };

    for (let i = 1; i < args.length; i += 2) {
        const flag = args[i];
        const value = args[i + 1];

        switch (flag) {
            case '--adult':
                options.adult = parseInt(value) || 0;
                break;
            case '--output':
            case '-o':
                options.output = value;
                break;
            case '--config':
            case '-c':
                options.config = value;
                break;
            default:
                console.error(`未知选项: ${flag}`);
                process.exit(1);
        }
    }

    return { storyIds, options };
}

// 保存数据到文件
function saveToFile(data, filePath) {
    try {
        const jsonData = JSON.stringify(data, null, 2);
        fs.writeFileSync(filePath, jsonData, 'utf8');
        console.log(`数据已保存到: ${filePath}`);
    } catch (error) {
        console.error('保存文件失败:', error.message);
    }
}

// 主函数
async function main() {
    try {
        const { storyIds, options } = parseArgs();
        
        // 加载配置
        const config = loadConfig();
        
        // 创建模拟器实例
        const simulator = new StoryResourceSimulator({
            baseUrl: config.server.baseUrl,
            apiPath: config.server.apiPath,
            headers: {
                ...config.headers,
                ...(config.auth.gameVersion && { 'X-Game-Version': config.auth.gameVersion })
            }
        });

        // 设置认证信息
        if (config.auth.sessionToken && config.auth.userId) {
            simulator.setAuth(config.auth.sessionToken, config.auth.userId);
        }

        console.log(`正在获取Story资源: ${storyIds.join(', ')}`);
        console.log(`成人内容标志: ${options.adult}`);
        console.log(`服务器地址: ${config.server.baseUrl}`);
        console.log('---');

        // 获取Story资源
        const startTime = Date.now();
        const storyData = await simulator.getStoryResource(storyIds, options.adult);
        const endTime = Date.now();

        console.log(`请求完成，耗时: ${endTime - startTime}ms`);
        console.log('---');

        // 输出结果
        if (options.output) {
            saveToFile(storyData, options.output);
        } else {
            console.log('Story资源数据:');
            console.log(JSON.stringify(storyData, null, 2));
        }

    } catch (error) {
        console.error('获取Story资源失败:', error.message);
        
        if (config.debug) {
            console.error('详细错误信息:', error);
        }
        
        process.exit(1);
    }
}

// 运行主函数
if (require.main === module) {
    main();
}

module.exports = { main, parseArgs, loadConfig };
